Collecting python-Levenshtein
  Downloading python_levenshtein-0.27.1-py3-none-any.whl.metadata (3.7 kB)
Collecting Levenshtein==0.27.1 (from python-Levenshtein)
  Downloading levenshtein-0.27.1-cp313-cp313-macosx_11_0_arm64.whl.metadata (3.6 kB)
Collecting rapidfuzz<4.0.0,>=3.9.0 (from Levenshtein==0.27.1->python-Levenshtein)
  Using cached rapidfuzz-3.13.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (12 kB)
Downloading python_levenshtein-0.27.1-py3-none-any.whl (9.4 kB)
Downloading levenshtein-0.27.1-cp313-cp313-macosx_11_0_arm64.whl (155 kB)
Using cached rapidfuzz-3.13.0-cp313-cp313-macosx_11_0_arm64.whl (1.4 MB)
Installing collected packages: rapidfuzz, Levenshtein, python-Levenshtein

Successfully installed Lev<PERSON>htein-0.27.1 python-Levenshtein-0.27.1 rapidfuzz-3.13.0
