# Core ontology processing dependencies
owlready2>=0.46
rdflib>=7.0.0
sparqlwrapper>=2.0.0

# Scientific computing and data analysis
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0
scipy>=1.10.0

# Natural language processing
nltk>=3.8
spacy>=3.7.0
python-Levenshtein>=0.21.0

# Web scraping and API access
requests>=2.31.0
beautifulsoup4>=4.12.0
scholarly>=1.7.0

# Testing framework
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-asyncio>=0.21.0

# Development tools
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0

# Configuration management
pyyaml>=6.0
python-dotenv>=1.0.0

# Logging and monitoring
loguru>=0.7.0

# Database connectivity (for future use)
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0

# Async support
aiohttp>=3.8.0
asyncio-throttle>=1.0.0

# Data validation
pydantic>=2.0.0

# Progress bars and utilities
tqdm>=4.65.0
click>=8.1.0
