"""
Tests for the PMCClient class.

This module contains comprehensive tests for PubMed Central (PMC) API client
functionality including authentication, article download, and rate limiting.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from typing import Dict, List, Any
import json
import tempfile
from pathlib import Path

from tests.literature.test_base import LiteratureTestBase


class TestPMCClient(LiteratureTestBase):
    """Test cases for PMCClient class."""
    
    def test_pmc_client_initialization(self):
        """Test PMCClient initialization with default and custom config."""
        from src.literature.pmc_client import PMCClient

        # Test default initialization
        client = PMCClient()

        assert client.config is not None
        assert hasattr(client, 'logger')
        assert hasattr(client, 'session')
        assert client.base_url is not None

        # Test with custom config - mock the config file loading to avoid interference
        custom_config = {
            'api_key': 'test_key',
            'email': '<EMAIL>',
            'rate_limit_delay': 2.0,
            'max_retries': 5
        }

        with patch('pathlib.Path.exists', return_value=False):  # Disable YAML config loading
            client_custom = PMCClient(custom_config)

        assert client_custom.config['api_key'] == 'test_key'
        assert client_custom.config['email'] == '<EMAIL>'
        assert client_custom.config['rate_limit_delay'] == 2.0
        assert client_custom.config['max_retries'] == 5
    
    def test_authenticate_success(self):
        """Test successful PMC authentication."""
        from src.literature.pmc_client import PMCClient
        
        client = PMCClient({
            'api_key': 'valid_test_key',
            'email': '<EMAIL>'
        })
        
        # Mock successful authentication response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'esearchresult': {
                'count': '1',
                'idlist': ['test']
            }
        }
        
        with patch.object(client.session, 'get', return_value=mock_response):
            result = client.authenticate()
        
        assert result is True
        assert client.is_authenticated is True
        assert client.last_auth_check is not None
    
    def test_authenticate_invalid_api_key(self):
        """Test authentication with invalid API key."""
        from src.literature.pmc_client import PMCClient
        
        client = PMCClient({
            'api_key': 'invalid_key',
            'email': '<EMAIL>'
        })
        
        # Mock authentication failure response
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            'error': 'Invalid API key'
        }
        
        with patch.object(client.session, 'get', return_value=mock_response):
            result = client.authenticate()
        
        assert result is False
        assert client.is_authenticated is False
        assert 'Invalid API key' in str(client.last_error)
    
    def test_authenticate_missing_email(self):
        """Test authentication with missing email."""
        from src.literature.pmc_client import PMCClient
        
        client = PMCClient({
            'api_key': 'test_key',
            'email': None
        })
        
        result = client.authenticate()
        
        assert result is False
        assert client.is_authenticated is False
        assert 'email' in str(client.last_error).lower()
    
    def test_authenticate_network_error(self):
        """Test authentication with network error."""
        from src.literature.pmc_client import PMCClient
        
        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })
        
        # Mock network error
        with patch.object(client.session, 'get', side_effect=ConnectionError("Network error")):
            result = client.authenticate()
        
        assert result is False
        assert client.is_authenticated is False
        assert 'network' in str(client.last_error).lower()
    
    def test_authenticate_timeout(self):
        """Test authentication with timeout."""
        from src.literature.pmc_client import PMCClient
        
        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>',
            'timeout': 1.0
        })
        
        # Mock timeout error
        with patch.object(client.session, 'get', side_effect=TimeoutError("Request timeout")):
            result = client.authenticate()
        
        assert result is False
        assert client.is_authenticated is False
        assert 'timeout' in str(client.last_error).lower()
    
    def test_authenticate_with_retry(self):
        """Test authentication with retry logic."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>',
            'max_retries': 3
        })

        # Mock successful response (the session handles retries automatically)
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'esearchresult': {
                'count': '1',
                'idlist': ['test']
            }
        }

        with patch.object(client.session, 'get', return_value=mock_response):
            result = client.authenticate()

        assert result is True
        assert client.is_authenticated is True
    
    def test_authenticate_rate_limiting(self):
        """Test authentication respects rate limiting."""
        from src.literature.pmc_client import PMCClient
        import time
        
        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>',
            'rate_limit_delay': 0.1  # Short delay for testing
        })
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'esearchresult': {
                'count': '1',
                'idlist': ['test']
            }
        }
        
        with patch.object(client.session, 'get', return_value=mock_response):
            start_time = time.time()

            # Make two authentication calls - disable caching for this test
            client.is_authenticated = False
            client.last_auth_check = None
            client.authenticate()

            client.is_authenticated = False
            client.last_auth_check = None
            client.authenticate()

            elapsed_time = time.time() - start_time

            # Should have waited at least the rate limit delay
            assert elapsed_time >= 0.1
    
    def test_authenticate_caching(self):
        """Test authentication result caching."""
        from src.literature.pmc_client import PMCClient
        
        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>',
            'auth_cache_duration': 300  # 5 minutes
        })
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'esearchresult': {
                'count': '1',
                'idlist': ['test']
            }
        }
        
        with patch.object(client.session, 'get', return_value=mock_response) as mock_get:
            # First authentication
            result1 = client.authenticate()
            
            # Second authentication (should use cache)
            result2 = client.authenticate()
            
            assert result1 is True
            assert result2 is True
            
            # Should only make one actual API call due to caching
            assert mock_get.call_count == 1
    
    def test_authenticate_cache_expiry(self):
        """Test authentication cache expiry."""
        from src.literature.pmc_client import PMCClient
        import time
        
        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>',
            'auth_cache_duration': 0.1  # Very short cache for testing
        })
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'esearchresult': {
                'count': '1',
                'idlist': ['test']
            }
        }
        
        with patch.object(client.session, 'get', return_value=mock_response) as mock_get:
            # First authentication
            client.authenticate()
            
            # Wait for cache to expire
            time.sleep(0.2)
            
            # Second authentication (should make new API call)
            client.authenticate()
            
            # Should make two API calls due to cache expiry
            assert mock_get.call_count == 2
    
    def test_authenticate_validation_parameters(self):
        """Test authentication parameter validation."""
        from src.literature.pmc_client import PMCClient
        
        # Test with empty API key
        client = PMCClient({
            'api_key': '',
            'email': '<EMAIL>'
        })
        
        result = client.authenticate()
        assert result is False
        assert 'api key' in str(client.last_error).lower()
        
        # Test with invalid email format
        client = PMCClient({
            'api_key': 'test_key',
            'email': 'invalid_email'
        })
        
        result = client.authenticate()
        assert result is False
        assert 'email' in str(client.last_error).lower()
    
    def test_authenticate_environment_variables(self):
        """Test authentication using environment variables."""
        from src.literature.pmc_client import PMCClient
        
        # Test with environment variables
        with patch.dict('os.environ', {
            'PMC_API_KEY': 'env_test_key',
            'PMC_EMAIL': '<EMAIL>'
        }):
            client = PMCClient()
            
            # Should load credentials from environment
            assert client.config['api_key'] == 'env_test_key'
            assert client.config['email'] == '<EMAIL>'
    
    def test_authenticate_logging(self):
        """Test authentication logging."""
        from src.literature.pmc_client import PMCClient
        
        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'esearchresult': {
                'count': '1',
                'idlist': ['test']
            }
        }
        
        with patch.object(client.session, 'get', return_value=mock_response):
            with patch.object(client.logger, 'info') as mock_log_info:
                with patch.object(client.logger, 'error') as mock_log_error:
                    result = client.authenticate()

                    assert result is True
                    mock_log_info.assert_called()
                    mock_log_error.assert_not_called()
    
    def test_is_authenticated_property(self):
        """Test is_authenticated property behavior."""
        from src.literature.pmc_client import PMCClient
        
        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })
        
        # Initially not authenticated
        assert client.is_authenticated is False
        
        # Mock successful authentication
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'esearchresult': {
                'count': '1',
                'idlist': ['test']
            }
        }
        
        with patch.object(client.session, 'get', return_value=mock_response):
            client.authenticate()
            assert client.is_authenticated is True

    def test_download_articles_success(self):
        """Test successful article download."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>',
            'rate_limit_delay': 0.1
        })

        # Mock authentication
        client.is_authenticated = True

        # Mock successful download response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b'<article>Test article content</article>'
        mock_response.headers = {'content-type': 'application/xml'}

        article_ids = ['PMC123456', 'PMC789012']

        with patch.object(client.session, 'get', return_value=mock_response):
            results = client.download_articles(article_ids)

        assert len(results) == 2
        assert all('content' in result for result in results)
        assert all('pmc_id' in result for result in results)
        assert all('status' in result for result in results)
        assert all(result['status'] == 'success' for result in results)

    def test_download_articles_with_rate_limiting(self):
        """Test article download respects rate limiting."""
        from src.literature.pmc_client import PMCClient
        import time

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>',
            'rate_limit_delay': 0.2  # 200ms delay for testing
        })

        # Mock authentication
        client.is_authenticated = True

        # Mock successful download response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b'<article>Test article content</article>'
        mock_response.headers = {'content-type': 'application/xml'}

        article_ids = ['PMC123456', 'PMC789012', 'PMC345678']

        with patch.object(client.session, 'get', return_value=mock_response):
            start_time = time.time()
            results = client.download_articles(article_ids)
            elapsed_time = time.time() - start_time

        # Should have waited for rate limiting between requests
        # With 3 articles and 0.2s delay, should take at least 0.4s (2 delays)
        assert elapsed_time >= 0.4
        assert len(results) == 3

    def test_download_articles_authentication_required(self):
        """Test download articles requires authentication."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Not authenticated
        client.is_authenticated = False

        article_ids = ['PMC123456']

        with pytest.raises(ValueError, match="Authentication required"):
            client.download_articles(article_ids)

    def test_download_articles_empty_list(self):
        """Test download articles with empty list."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Mock authentication
        client.is_authenticated = True

        results = client.download_articles([])
        assert results == []

    def test_download_articles_invalid_ids(self):
        """Test download articles with invalid PMC IDs."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Mock authentication
        client.is_authenticated = True

        # Test with invalid ID format
        with pytest.raises(ValueError, match="Invalid PMC ID format"):
            client.download_articles(['invalid_id'])

        # Test with None in list
        with pytest.raises(ValueError, match="PMC ID cannot be None or empty"):
            client.download_articles([None])

        # Test with empty string
        with pytest.raises(ValueError, match="PMC ID cannot be None or empty"):
            client.download_articles([''])

    def test_download_articles_http_error(self):
        """Test download articles with HTTP error."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Mock authentication
        client.is_authenticated = True

        # Mock HTTP error response
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.text = 'Article not found'

        article_ids = ['PMC123456']

        with patch.object(client.session, 'get', return_value=mock_response):
            results = client.download_articles(article_ids)

        assert len(results) == 1
        assert results[0]['status'] == 'error'
        assert 'error' in results[0]
        assert '404' in results[0]['error']

    def test_download_articles_network_error(self):
        """Test download articles with network error."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Mock authentication
        client.is_authenticated = True

        article_ids = ['PMC123456']

        with patch.object(client.session, 'get', side_effect=ConnectionError("Network error")):
            results = client.download_articles(article_ids)

        assert len(results) == 1
        assert results[0]['status'] == 'error'
        assert 'network error' in results[0]['error'].lower()

    def test_download_articles_timeout(self):
        """Test download articles with timeout."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>',
            'timeout': 1.0
        })

        # Mock authentication
        client.is_authenticated = True

        article_ids = ['PMC123456']

        with patch.object(client.session, 'get', side_effect=TimeoutError("Request timeout")):
            results = client.download_articles(article_ids)

        assert len(results) == 1
        assert results[0]['status'] == 'error'
        assert 'timeout' in results[0]['error'].lower()

    def test_download_articles_with_retry_logic(self):
        """Test download articles with retry logic for failed requests."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>',
            'max_retries': 3
        })

        # Mock authentication
        client.is_authenticated = True

        # Mock response that succeeds after retries (handled by session)
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b'<article>Test article content</article>'
        mock_response.headers = {'content-type': 'application/xml'}

        article_ids = ['PMC123456']

        with patch.object(client.session, 'get', return_value=mock_response):
            results = client.download_articles(article_ids)

        assert len(results) == 1
        assert results[0]['status'] == 'success'

    def test_download_articles_mixed_results(self):
        """Test download articles with mixed success and failure results."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Mock authentication
        client.is_authenticated = True

        # Mock responses - success for first, error for second
        success_response = Mock()
        success_response.status_code = 200
        success_response.content = b'<article>Test article content</article>'
        success_response.headers = {'content-type': 'application/xml'}

        error_response = Mock()
        error_response.status_code = 404
        error_response.text = 'Article not found'

        article_ids = ['PMC123456', 'PMC789012']

        with patch.object(client.session, 'get', side_effect=[success_response, error_response]):
            results = client.download_articles(article_ids)

        assert len(results) == 2
        assert results[0]['status'] == 'success'
        assert results[1]['status'] == 'error'

    def test_download_articles_progress_callback(self):
        """Test download articles with progress callback."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Mock authentication
        client.is_authenticated = True

        # Mock successful download response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b'<article>Test article content</article>'
        mock_response.headers = {'content-type': 'application/xml'}

        article_ids = ['PMC123456', 'PMC789012']
        progress_calls = []

        def progress_callback(current, total, pmc_id):
            progress_calls.append((current, total, pmc_id))

        with patch.object(client.session, 'get', return_value=mock_response):
            results = client.download_articles(article_ids, progress_callback=progress_callback)

        assert len(results) == 2
        assert len(progress_calls) == 2
        assert progress_calls[0] == (1, 2, 'PMC123456')
        assert progress_calls[1] == (2, 2, 'PMC789012')

    def test_download_articles_content_validation(self):
        """Test download articles validates content format."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Mock authentication
        client.is_authenticated = True

        # Mock response with invalid XML content
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b'Invalid XML content'
        mock_response.headers = {'content-type': 'text/html'}

        article_ids = ['PMC123456']

        with patch.object(client.session, 'get', return_value=mock_response):
            results = client.download_articles(article_ids, validate_xml=True)

        assert len(results) == 1
        assert results[0]['status'] == 'error'
        assert 'validation' in results[0]['error'].lower() or 'xml' in results[0]['error'].lower()

    @pytest.mark.asyncio
    async def test_download_articles_async_method_exists(self):
        """Test that async download method exists and has correct signature."""
        from src.literature.pmc_client import PMCClient
        import inspect

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Check that the async method exists
        assert hasattr(client, 'download_articles_async')

        # Check that it's a coroutine function
        assert inspect.iscoroutinefunction(client.download_articles_async)

        # Check method signature
        sig = inspect.signature(client.download_articles_async)
        expected_params = ['article_ids', 'progress_callback', 'validate_xml', 'max_concurrent']
        actual_params = list(sig.parameters.keys())

        for param in expected_params:
            assert param in actual_params

    @pytest.mark.asyncio
    async def test_download_articles_async_authentication_required(self):
        """Test async download articles requires authentication."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Not authenticated
        client.is_authenticated = False

        article_ids = ['PMC123456']

        with pytest.raises(ValueError, match="Authentication required"):
            await client.download_articles_async(article_ids)

    @pytest.mark.asyncio
    async def test_download_articles_async_empty_list(self):
        """Test async download articles with empty list."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Mock authentication
        client.is_authenticated = True

        results = await client.download_articles_async([])
        assert results == []

    @pytest.mark.asyncio
    async def test_download_articles_async_invalid_ids(self):
        """Test async download articles with invalid PMC IDs."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Mock authentication
        client.is_authenticated = True

        # Test with invalid ID format
        with pytest.raises(ValueError, match="Invalid PMC ID format"):
            await client.download_articles_async(['invalid_id'])

    def test_async_method_integration_with_sync(self):
        """Test that async method integrates well with sync methods."""
        from src.literature.pmc_client import PMCClient

        client = PMCClient({
            'api_key': 'test_key',
            'email': '<EMAIL>'
        })

        # Both sync and async methods should exist
        assert hasattr(client, 'download_articles')
        assert hasattr(client, 'download_articles_async')

        # They should have similar parameter signatures
        import inspect
        sync_sig = inspect.signature(client.download_articles)
        async_sig = inspect.signature(client.download_articles_async)

        # Both should have article_ids parameter
        assert 'article_ids' in sync_sig.parameters
        assert 'article_ids' in async_sig.parameters

        # Both should have progress_callback parameter
        assert 'progress_callback' in sync_sig.parameters
        assert 'progress_callback' in async_sig.parameters


class TestPublisherAPIManager(LiteratureTestBase):
    """Test cases for PublisherAPIManager class."""

    def test_publisher_api_manager_initialization(self):
        """Test PublisherAPIManager initialization."""
        from src.literature.publisher_api_manager import PublisherAPIManager

        manager = PublisherAPIManager()

        assert manager is not None
        assert hasattr(manager, 'registered_apis')
        assert hasattr(manager, 'config')
        assert isinstance(manager.registered_apis, dict)

    def test_register_apis_success(self):
        """Test successful API registration."""
        from src.literature.publisher_api_manager import PublisherAPIManager

        manager = PublisherAPIManager()

        # Test API configuration
        api_configs = {
            'springer': {
                'base_url': 'https://api.springer.com',
                'api_key': 'test_springer_key',
                'rate_limit': 1.0,
                'timeout': 30
            },
            'elsevier': {
                'base_url': 'https://api.elsevier.com',
                'api_key': 'test_elsevier_key',
                'rate_limit': 2.0,
                'timeout': 45
            }
        }

        result = manager.register_apis(api_configs)

        assert result is True
        assert len(manager.registered_apis) == 2
        assert 'springer' in manager.registered_apis
        assert 'elsevier' in manager.registered_apis

        # Check API client properties
        springer_client = manager.registered_apis['springer']
        assert hasattr(springer_client, 'base_url')
        assert hasattr(springer_client, 'api_key')
        assert hasattr(springer_client, 'rate_limit')

    def test_register_apis_empty_config(self):
        """Test API registration with empty configuration."""
        from src.literature.publisher_api_manager import PublisherAPIManager

        manager = PublisherAPIManager()

        result = manager.register_apis({})

        assert result is True
        assert len(manager.registered_apis) == 0

    def test_register_apis_invalid_config(self):
        """Test API registration with invalid configuration."""
        from src.literature.publisher_api_manager import PublisherAPIManager

        manager = PublisherAPIManager()

        # Missing required fields
        invalid_config = {
            'springer': {
                'base_url': 'https://api.springer.com'
                # Missing api_key
            }
        }

        with pytest.raises(ValueError, match="Missing required field"):
            manager.register_apis(invalid_config)

    def test_register_apis_duplicate_registration(self):
        """Test registering the same API twice."""
        from src.literature.publisher_api_manager import PublisherAPIManager

        manager = PublisherAPIManager()

        api_config = {
            'springer': {
                'base_url': 'https://api.springer.com',
                'api_key': 'test_key',
                'rate_limit': 1.0,
                'timeout': 30
            }
        }

        # First registration
        result1 = manager.register_apis(api_config)
        assert result1 is True

        # Second registration (should update existing)
        api_config['springer']['api_key'] = 'updated_key'
        result2 = manager.register_apis(api_config)
        assert result2 is True

        # Should still have only one API registered
        assert len(manager.registered_apis) == 1
        assert manager.registered_apis['springer'].api_key == 'updated_key'

    def test_register_apis_with_custom_client_class(self):
        """Test API registration with custom client class."""
        from src.literature.publisher_api_manager import PublisherAPIManager

        manager = PublisherAPIManager()

        # Mock custom client class
        class MockPublisherClient:
            def __init__(self, config):
                self.config = config
                self.base_url = config['base_url']
                self.api_key = config['api_key']

        api_config = {
            'custom_publisher': {
                'base_url': 'https://api.custom.com',
                'api_key': 'test_key',
                'client_class': MockPublisherClient
            }
        }

        result = manager.register_apis(api_config)

        assert result is True
        assert len(manager.registered_apis) == 1
        assert isinstance(manager.registered_apis['custom_publisher'], MockPublisherClient)

    def test_register_apis_validation_errors(self):
        """Test API registration validation errors."""
        from src.literature.publisher_api_manager import PublisherAPIManager

        manager = PublisherAPIManager()

        # Test invalid URL format
        invalid_url_config = {
            'springer': {
                'base_url': 'not_a_valid_url',
                'api_key': 'test_key'
            }
        }

        with pytest.raises(ValueError, match="Invalid URL format"):
            manager.register_apis(invalid_url_config)

        # Test empty API key
        empty_key_config = {
            'springer': {
                'base_url': 'https://api.springer.com',
                'api_key': ''
            }
        }

        with pytest.raises(ValueError, match="API key cannot be empty"):
            manager.register_apis(empty_key_config)

    def test_register_apis_with_environment_variables(self):
        """Test API registration using environment variables."""
        from src.literature.publisher_api_manager import PublisherAPIManager

        manager = PublisherAPIManager()

        # Test with environment variable placeholders
        api_config = {
            'springer': {
                'base_url': 'https://api.springer.com',
                'api_key': '${SPRINGER_API_KEY}',
                'rate_limit': 1.0
            }
        }

        with patch.dict('os.environ', {'SPRINGER_API_KEY': 'env_test_key'}):
            result = manager.register_apis(api_config)

        assert result is True
        assert manager.registered_apis['springer'].api_key == 'env_test_key'

    def test_register_apis_logging(self):
        """Test API registration logging."""
        from src.literature.publisher_api_manager import PublisherAPIManager

        manager = PublisherAPIManager()

        api_config = {
            'springer': {
                'base_url': 'https://api.springer.com',
                'api_key': 'test_key',
                'rate_limit': 1.0
            }
        }

        with patch.object(manager.logger, 'info') as mock_log_info:
            result = manager.register_apis(api_config)

        assert result is True
        mock_log_info.assert_called()

        # Check that sensitive information is not logged
        log_calls = [call.args[0] for call in mock_log_info.call_args_list]
        for call in log_calls:
            assert 'test_key' not in call  # API key should not be in logs


class TestQuotaManagementAndRateLimiting(LiteratureTestBase):
    """Test cases for quota management and rate limiting functionality."""

    def test_quota_tracker_initialization(self):
        """Test QuotaTracker initialization."""
        from src.literature.quota_manager import QuotaTracker

        config = {
            'daily_limit': 1000,
            'hourly_limit': 100,
            'minute_limit': 10
        }

        tracker = QuotaTracker('test_api', config)

        assert tracker is not None
        assert tracker.api_name == 'test_api'
        assert tracker.daily_limit == 1000
        assert tracker.hourly_limit == 100
        assert tracker.minute_limit == 10
        assert tracker.current_usage == {'daily': 0, 'hourly': 0, 'minute': 0}

    def test_quota_tracker_usage_increment(self):
        """Test quota usage increment."""
        from src.literature.quota_manager import QuotaTracker

        config = {
            'daily_limit': 1000,
            'hourly_limit': 100,
            'minute_limit': 10
        }

        tracker = QuotaTracker('test_api', config)

        # Test single increment
        result = tracker.increment_usage()
        assert result is True
        assert tracker.current_usage['daily'] == 1
        assert tracker.current_usage['hourly'] == 1
        assert tracker.current_usage['minute'] == 1

        # Test multiple increments
        for i in range(5):
            tracker.increment_usage()

        assert tracker.current_usage['daily'] == 6
        assert tracker.current_usage['hourly'] == 6
        assert tracker.current_usage['minute'] == 6

    def test_quota_tracker_limit_checking(self):
        """Test quota limit checking."""
        from src.literature.quota_manager import QuotaTracker

        config = {
            'daily_limit': 10,
            'hourly_limit': 5,
            'minute_limit': 2
        }

        tracker = QuotaTracker('test_api', config)

        # Should be under limit initially
        assert tracker.is_under_limit() is True

        # Increment to minute limit
        tracker.increment_usage()
        tracker.increment_usage()

        # Should be at minute limit
        assert tracker.is_under_limit() is False
        assert tracker.get_limiting_quota() == 'minute'

    def test_quota_tracker_reset_functionality(self):
        """Test quota reset functionality."""
        from src.literature.quota_manager import QuotaTracker

        config = {
            'daily_limit': 1000,
            'hourly_limit': 100,
            'minute_limit': 10
        }

        tracker = QuotaTracker('test_api', config)

        # Add some usage
        for i in range(5):
            tracker.increment_usage()

        assert tracker.current_usage['minute'] == 5

        # Reset minute quota
        tracker.reset_quota('minute')
        assert tracker.current_usage['minute'] == 0
        assert tracker.current_usage['hourly'] == 5  # Should remain unchanged

        # Reset all quotas
        tracker.reset_all_quotas()
        assert tracker.current_usage['daily'] == 0
        assert tracker.current_usage['hourly'] == 0
        assert tracker.current_usage['minute'] == 0

    def test_rate_limiter_initialization(self):
        """Test RateLimiter initialization."""
        from src.literature.quota_manager import RateLimiter

        config = {
            'requests_per_second': 2.0,
            'burst_limit': 5
        }

        limiter = RateLimiter('test_api', config)

        assert limiter is not None
        assert limiter.api_name == 'test_api'
        assert limiter.requests_per_second == 2.0
        assert limiter.burst_limit == 5
        assert limiter.tokens == 5  # Should start with full burst

    def test_rate_limiter_token_consumption(self):
        """Test rate limiter token consumption."""
        from src.literature.quota_manager import RateLimiter
        import time

        config = {
            'requests_per_second': 0.1,  # Very slow refill to avoid timing issues
            'burst_limit': 3
        }

        limiter = RateLimiter('test_api', config)

        # Should allow requests up to burst limit
        assert limiter.can_make_request() is True
        limiter.consume_token()
        assert abs(limiter.tokens - 2) < 0.01  # Allow for small timing differences

        assert limiter.can_make_request() is True
        limiter.consume_token()
        assert abs(limiter.tokens - 1) < 0.01  # Allow for small timing differences

        assert limiter.can_make_request() is True
        limiter.consume_token()
        assert limiter.tokens < 0.01  # Should be close to 0

        # Should be rate limited now
        assert limiter.can_make_request() is False

    def test_rate_limiter_token_refill(self):
        """Test rate limiter token refill."""
        from src.literature.quota_manager import RateLimiter
        import time

        config = {
            'requests_per_second': 5.0,  # 5 tokens per second
            'burst_limit': 2
        }

        limiter = RateLimiter('test_api', config)

        # Consume all tokens quickly to minimize refill
        initial_time = limiter.last_refill
        limiter.tokens = 0  # Set directly to avoid timing issues
        limiter.last_refill = initial_time

        # Wait for refill (0.5 seconds should add 2.5 tokens, capped at burst_limit)
        time.sleep(0.5)
        limiter._refill_tokens()

        # Should have refilled to burst limit
        assert limiter.tokens == 2

    def test_rate_limiter_wait_time_calculation(self):
        """Test rate limiter wait time calculation."""
        from src.literature.quota_manager import RateLimiter

        config = {
            'requests_per_second': 2.0,  # 0.5 seconds per token
            'burst_limit': 1
        }

        limiter = RateLimiter('test_api', config)

        # Consume the only token
        limiter.consume_token()
        assert limiter.tokens == 0

        # Should need to wait for next token
        wait_time = limiter.get_wait_time()
        assert wait_time > 0
        assert wait_time <= 0.5  # Should be at most 0.5 seconds

    def test_quota_manager_integration(self):
        """Test QuotaManager integration with multiple APIs."""
        from src.literature.quota_manager import QuotaManager

        config = {
            'springer': {
                'daily_limit': 1000,
                'hourly_limit': 100,
                'requests_per_second': 1.0,
                'burst_limit': 5
            },
            'elsevier': {
                'daily_limit': 500,
                'hourly_limit': 50,
                'requests_per_second': 0.5,
                'burst_limit': 3
            }
        }

        manager = QuotaManager(config)

        assert manager is not None
        assert 'springer' in manager.quota_trackers
        assert 'elsevier' in manager.rate_limiters

        # Test request checking
        assert manager.can_make_request('springer') is True
        assert manager.can_make_request('elsevier') is True

        # Test usage tracking
        manager.record_request('springer')
        springer_tracker = manager.quota_trackers['springer']
        assert springer_tracker.current_usage['daily'] == 1

    def test_quota_manager_rate_limiting_enforcement(self):
        """Test QuotaManager rate limiting enforcement."""
        from src.literature.quota_manager import QuotaManager

        config = {
            'test_api': {
                'daily_limit': 1000,
                'hourly_limit': 100,
                'requests_per_second': 1.0,
                'burst_limit': 2
            }
        }

        manager = QuotaManager(config)

        # Should allow initial requests up to burst limit
        assert manager.can_make_request('test_api') is True
        manager.record_request('test_api')

        assert manager.can_make_request('test_api') is True
        manager.record_request('test_api')

        # Should be rate limited after burst
        assert manager.can_make_request('test_api') is False

        # Get wait time
        wait_time = manager.get_wait_time('test_api')
        assert wait_time > 0

    def test_quota_manager_quota_limit_enforcement(self):
        """Test QuotaManager quota limit enforcement."""
        from src.literature.quota_manager import QuotaManager

        config = {
            'test_api': {
                'daily_limit': 2,
                'hourly_limit': 2,
                'minute_limit': 2,
                'requests_per_second': 10.0,  # High rate to avoid rate limiting
                'burst_limit': 10
            }
        }

        manager = QuotaManager(config)

        # Make requests up to quota limit
        assert manager.can_make_request('test_api') is True
        manager.record_request('test_api')

        assert manager.can_make_request('test_api') is True
        manager.record_request('test_api')

        # Should be quota limited now
        assert manager.can_make_request('test_api') is False

        # Check which quota is limiting
        limiting_quota = manager.get_limiting_quota('test_api')
        assert limiting_quota in ['daily', 'hourly', 'minute']

    def test_quota_manager_error_handling(self):
        """Test QuotaManager error handling for unknown APIs."""
        from src.literature.quota_manager import QuotaManager

        manager = QuotaManager({})

        # Should handle unknown API gracefully
        assert manager.can_make_request('unknown_api') is False

        # Should not crash on recording request for unknown API
        manager.record_request('unknown_api')  # Should not raise exception

        # Should return None for wait time of unknown API
        wait_time = manager.get_wait_time('unknown_api')
        assert wait_time is None

    def test_quota_manager_reset_functionality(self):
        """Test QuotaManager reset functionality."""
        from src.literature.quota_manager import QuotaManager

        config = {
            'test_api': {
                'daily_limit': 100,
                'hourly_limit': 50,
                'minute_limit': 10,
                'requests_per_second': 1.0,
                'burst_limit': 5
            }
        }

        manager = QuotaManager(config)

        # Make some requests
        for i in range(3):
            manager.record_request('test_api')

        tracker = manager.quota_trackers['test_api']
        assert tracker.current_usage['daily'] == 3

        # Reset quotas
        manager.reset_quotas('test_api')
        assert tracker.current_usage['daily'] == 0
        assert tracker.current_usage['hourly'] == 0
        assert tracker.current_usage['minute'] == 0

    def test_middleware_integration(self):
        """Test middleware integration with quota management."""
        from src.literature.quota_manager import QuotaMiddleware

        config = {
            'test_api': {
                'daily_limit': 100,
                'requests_per_second': 2.0,
                'burst_limit': 3
            }
        }

        middleware = QuotaMiddleware(config)

        # Test request interception
        def mock_request_func():
            return "success"

        # Should allow request initially
        result = middleware.intercept_request('test_api', mock_request_func)
        assert result == "success"

        # Should track the request
        tracker = middleware.quota_manager.quota_trackers['test_api']
        assert tracker.current_usage['daily'] == 1
