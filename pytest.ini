[pytest]
minversion = 7.4
addopts = -ra -q --strict-markers --strict-config
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

markers =
    ontology: marks tests as ontology-related
    corpus: marks tests as corpus analysis related
    clustering: marks tests as clustering algorithm related
    validation: marks tests as validation system related
    sparql: marks tests as SPARQL query related
    integration: marks tests as integration tests
    slow: marks tests as slow running
    unit: marks tests as unit tests
    functional: marks tests as functional tests
    asyncio: marks tests as async/await tests

filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
